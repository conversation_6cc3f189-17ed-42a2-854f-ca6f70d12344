//
//  SupermarketLoyaltyMembershipViewController.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON>@HappyFresh on 14/02/23.
//  Copyright © 2023 HappyFresh Inc. All rights reserved.
//

import UIKit
import SVProgressHUD
import Shimmer

final class SupermarketLoyaltyMembershipViewController: BaseViewController {
    init(viewModel: SupermarketLoyaltyMembershipViewModel) {
        self.viewModel = viewModel
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private let viewModel: SupermarketLoyaltyMembershipViewModel
    
    private let submissionRuleObservable = Observable<SupermarketLoyaltyMembershipSubmissionRule>(.init())
    private var inputViewModels = [SupermarketLoyaltyMembershipInputWidgetViewModel]()
    
    private let shimmerContainer = FBShimmeringView()
    
    private lazy var shopNowButton: UIButton = {
        let button = HappyButton(title: NSLocalizedString("Shop now", comment: ""),
                                accentColor: UIColor.hf_prime(),
                                type: HappyButton.HappyButtonType.Primary)
        button.addTarget(self, action: #selector(shopNowTapped), for: .touchUpInside)
        return button
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        leftBarButtonItemTypes = .close
        screenName = ScreenNameMSI
        
        addRootView()
        
        viewModel.title.onDidChange = { [weak self] title in
            DispatchQueue.main.async { [weak self] in
                self?.setupTitle(title)
            }
        }
        
        viewModel.widgets.onDidChange = { [weak self] widgets in
            DispatchQueue.main.async { [weak self] in
                self?.configureUI(widgets: widgets)
            }
        }
        
        viewModel.alert.onDidChange = { [weak self] alert in
            DispatchQueue.main.async { [weak self] in
                if let alert {
                    self?.showAlert(alert)
                }
            }
        }
        
        viewModel.successAlert.onDidChange = { [weak self] alert in
            DispatchQueue.main.async { [weak self] in
                if let alert {
                    self?.showPopUp(alert)
                }
            }
        }
        
        toggleShimmer()
        Task {
            await viewModel.load()
            toggleShimmer()
        }
    }
    
    override func closeButtonTapped(_ sender: UIBarButtonItem!) {
        super.closeButtonTapped(sender)
        viewModel.close()
    }
    
    @MainActor
    private func toggleShimmer() {
        if view.isEqual(shimmerContainer.superview) {
            shimmerContainer.isShimmering = false
            shimmerContainer.removeFromSuperview()
        } else {
            view.addSubview(shimmerContainer)
            shimmerContainer.frame = view.bounds
            shimmerContainer.contentView = SupermarketLoyaltyShimmeringView.fromNib()!
            shimmerContainer.isShimmering = true
        }
    }
    
    private func configureUI(widgets: [SupermarketLoyaltyMembershipWidgetViewModel]) {
        for widget in widgets {
            switch widget {
            case let widget as SupermarketLoyaltyMembershipLogoWidgetViewModel:
                insertLogo(widget)
            case let widget as SupermarketLoyaltyMembershipHeadingWidgetViewModel:
                insertMembershipInfoIfNeeded()
                membershipInfoInsertHeading(widget)
            case let widget as SupermarketLoyaltyMembershipBodyWidgetViewModel:
                insertMembershipInfoIfNeeded()
                membershipInfoInsertBody(widget)
            case let widget as SupermarketLoyaltyMembershipInputWidgetViewModel:
                insertInputField(widget)
            case let widget as SupermarketLoyaltyMembershipValueWidgetViewModel:
                insertValueField(widget)
            case let widget as SupermarketLoyaltyMembershipSubmissionWidgetViewModel:
                insertSubmitButton(widget)
                insertTncButton(widget)
            case let widget as SupermarketLoyaltyMembershipMessageWidgetViewModel:
                insertMessage(widget)
            case let widget as SupermarketLoyaltyMembershipBannerWidgetViewModel:
                insertBannerImage(widget)
            case let widget as SupermarketLoyaltyMembershipTncWidgetViewModel:
                insertTnc(widget)
            case _ as SupermarketLoyaltyMembershipShopNowWidgetViewModel:
                insertShopNow()
            default:
                break
            }
        }
    }
    
    private func showAlert(_ alert: HFAlert) {
        let popup = HFPopup(imageString: "hf_img_empty_state_error_200px",
                            title: alert.title,
                            subtitle: alert.message,
                            buttonTitle: "OK")
        showPopup(popup)
    }
    
    private func showPopUp(_ alert: SupermarketLoyaltyMembershipViewModel.SuccessAlert) {
        let popup = HFPopup(
            imageObject: UIImage(data: alert.imageData ?? Data()),
            title: alert.title ?? "",
            subtitle: alert.description ?? "",
            buttonTitle: alert.button ?? "",
            isHideCloseButton: true
        )
        popup.primaryButtonTappedBlock = { [weak self] in
            self?.viewModel.finishLink()
            self?.dismiss(animated: true)
        }
        showPopup(popup)
    }
    
    private let stack: UIStackView = {
        let stack = UIStackView(frame: .zero)
        stack.axis = .vertical
        stack.spacing = 20
        stack.alignment = .leading
        return stack
    }()
    
    private func addRootView() {
        let scroll = UIScrollView()
        scroll.addSubview(stack)
        stack.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(16)
            make.bottom.equalToSuperview()
            make.width.equalTo(scroll.snp.width).inset(16)
            make.centerX.equalToSuperview()
        }
        view.addSubview(scroll)
        scroll.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
            make.leading.equalTo(view.safeAreaLayoutGuide.snp.leading)
            make.trailing.equalTo(view.safeAreaLayoutGuide.snp.trailing)
        }
    }
    
    private func insertLogo(_ widget: SupermarketLoyaltyMembershipLogoWidgetViewModel) {
        let logoView = UIImageView()
        logoView.add_setImage(with: widget.logoURL)
        stack.addArrangedSubview(logoView)
        logoView.snp.makeConstraints { make in
            make.width.equalTo(51)
            make.height.equalTo(55)
        }
    }
    
    private func insertBannerImage(_ widget: SupermarketLoyaltyMembershipBannerWidgetViewModel) {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.clipsToBounds = true

        stack.addArrangedSubview(imageView)

        imageView.snp.makeConstraints { make in
            make.width.equalTo(UIScreen.main.bounds.width)
        }

        let placeholder = UIImage(named: "hf_img_placeholder_with_background_96px")
        imageView.add_setImage(with: widget.imageURL, placeHolderImage: placeholder) { (image, error)  in
            guard let image = image else { return }

            let ratio = image.size.height / image.size.width
            imageView.snp.remakeConstraints { make in
                make.width.equalTo(UIScreen.main.bounds.width)
                make.height.equalTo(imageView.snp.width).multipliedBy(ratio)
            }
        }
    }
    
    private func insertTnc(_ widget: SupermarketLoyaltyMembershipTncWidgetViewModel) {
        let textView = UITextView()
        textView.textColor = .hf_grey_100()
        textView.font = .hf_small_medium()
        textView.isEditable = false
        textView.isScrollEnabled = false
        textView.textContainerInset = .zero
        textView.textContainer.lineFragmentPadding = 0
        
        let attrString = widget.tnc?.htmlAttributedString ?? NSAttributedString(string: "")
        let mutableAttrString = NSMutableAttributedString(attributedString: attrString)
        mutableAttrString.enumerateAttribute(.font, in: NSRange(location: 0, length: mutableAttrString.length), options: []) { value, range, _ in
            let font = UIFont.hf_title_6() ?? UIFont.systemFont(ofSize: 14, weight: .regular)
            mutableAttrString.addAttribute(.font, value: font, range: range)
        }
        textView.attributedText = mutableAttrString
        
        stack.addArrangedSubview(textView)
        textView.snp.makeConstraints { make in
            make.width.equalToSuperview()
        }
    }
    
    private func insertShopNow() {
        let containerView = UIView()
        containerView.backgroundColor = .white
        
        view.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
        }
        
        containerView.addSubview(shopNowButton)
        shopNowButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).inset(12)
            make.height.equalTo(44)
        }
        
        containerView.snp.makeConstraints { make in
            make.top.equalTo(shopNowButton.snp.top).inset(-12)
        }
        
        let bottomSpacer = UIView()
        stack.addArrangedSubview(bottomSpacer)
        bottomSpacer.snp.makeConstraints { make in
            make.height.equalTo(containerView.snp.height)
        }
    }
    
    private let membershipInfoStack: UIStackView = {
        let stack = UIStackView(frame: .zero)
        stack.axis = .vertical
        stack.spacing = 5
        return stack
    }()
    
    
    
    private func insertMembershipInfoIfNeeded() {
        guard !stack.arrangedSubviews.contains(membershipInfoStack) else {
            return
        }
        stack.addArrangedSubview(membershipInfoStack)
        membershipInfoStack.snp.makeConstraints { make in
            make.width.equalToSuperview()
        }
    }
    
    private func membershipInfoInsertHeading(_ widget: SupermarketLoyaltyMembershipHeadingWidgetViewModel) {
        let heading = UILabel()
        heading.text = widget.heading
        heading.textColor = .hf_grey_100()
        heading.font = UIFont.hf_title_18()
        heading.numberOfLines = 0
        heading.lineBreakMode = .byWordWrapping
        membershipInfoStack.addArrangedSubview(heading)
        heading.snp.makeConstraints { make in
            make.width.equalToSuperview()
        }
    }
    
    private func membershipInfoInsertBody(_ widget: SupermarketLoyaltyMembershipBodyWidgetViewModel) {
        let body = UILabel()
        body.text = widget.body
        body.numberOfLines = 0
        body.lineBreakMode = .byWordWrapping
        body.textColor = .hf_grey_80()
        body.font = .hf_small()
        membershipInfoStack.addArrangedSubview(body)
        body.snp.makeConstraints { make in
            make.width.equalToSuperview()
        }
    }
    
    private var _onInfoTapClosure = [() -> Void]()
    private func insertInputField(_ widget: SupermarketLoyaltyMembershipInputWidgetViewModel) {
        
        let stack = UIStackView(frame: .zero)
        stack.axis = .vertical
        stack.spacing = 5
        stack.alignment = .leading
        
        let inputTitle = UILabel()
        inputTitle.text = widget.title
        inputTitle.numberOfLines = 0
        inputTitle.lineBreakMode = .byWordWrapping
        inputTitle.textColor = .hf_grey_120()
        inputTitle.font = .hf_small_medium()
        stack.addArrangedSubview(inputTitle)
        inputTitle.snp.makeConstraints { make in
            make.width.equalToSuperview()
        }
        
        
        let infoButton = UIButton()
        infoButton.setImage(UIImage(named: "hf_icon_help_blue"), for: .normal)
        
        let inputDescription = UILabel()
        inputDescription.text = widget.description
        inputDescription.numberOfLines = 0
        inputDescription.lineBreakMode = .byWordWrapping
        inputDescription.textColor = .hf_grey_120()
        inputDescription.font = .hf_micro()
        
        let hStack = UIStackView()
        hStack.axis = .horizontal
        hStack.spacing = 20
        hStack.addArrangedSubview(inputDescription)
        hStack.addArrangedSubview(infoButton)
        stack.addArrangedSubview(hStack)
        infoButton.snp.makeConstraints { make in
            make.width.height.equalTo(20)
        }
        
        infoButton.tag = _onInfoTapClosure.count
        _onInfoTapClosure.append({ [weak self] in
            let popup = HFPopup(
                imageObject: UIImage(data: widget.popupImageData ?? Data()),
                title: widget.popupTitle ?? "",
                subtitle: widget.popupDescription ?? "",
                buttonTitle: widget.popupButton ?? ""
            )
            if let imageHeight = widget.imageHeight.map(Int.init) {
                popup.imageHeight = imageHeight
            }
            self?.showPopup(popup)
            self?.viewModel.showPopUp(widget)
        })
        infoButton.addTarget(self, action: #selector(_onInfoButtonTap), for: .touchUpInside)
        
        let textField = UITextField()
        textField.placeholder = widget.placeholder
        textField.backgroundColor = UIColor.hf_white()
        textField.clipsToBounds = true
        textField.cornerRadius = 4
        textField.borderStyle = .none
        textField.borderColor = .hf_divider_default()
        textField.borderWidth = 1
        textField.font = UIFont.hf_small()
        textField.textColor = UIColor.hf_grey_100()
        textField.addLeftInset(16)
        textField.returnKeyType = .done
        textField.tag = submissionRuleObservable.value.inputs.count
        textField.autocapitalizationType = .none
        textField.keyboardType = widget.keyboardType
        inputViewModels.append(widget)
        submissionRuleObservable.value.inputs.append(textField.text ?? "")
        textField.addTarget(self, action: #selector(textFieldDidChange), for: .editingChanged)
        stack.addArrangedSubview(textField)
        textField.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalTo(48)
        }
        
        self.stack.addArrangedSubview(stack)
        stack.snp.makeConstraints { make in
            make.width.equalToSuperview()
        }
    }
    
    private func insertValueField(_ widget: SupermarketLoyaltyMembershipValueWidgetViewModel) {
        let title = UILabel()
        title.text = widget.title
        title.numberOfLines = 0
        title.textColor = .hf_grey_120()
        title.font = .hf_small_medium()
        
        let valueDescription = UILabel()
        valueDescription.text = widget.value
        valueDescription.numberOfLines = 0
        valueDescription.lineBreakMode = .byWordWrapping
        valueDescription.textColor = .hf_happy_100()
        valueDescription.font = .hf_small_medium()
        
        let view = UIView()
        view.addSubview(title)
        view.addSubview(valueDescription)
        
        title.snp.makeConstraints { make in
            make.leading.top.bottom.equalToSuperview()
        }
        valueDescription.snp.makeConstraints { make in
            make.trailing.top.bottom.equalToSuperview()
            make.leading.greaterThanOrEqualTo(title.snp.trailing).offset(8)
        }
        
        self.stack.addArrangedSubview(view)
        view.snp.makeConstraints { make in
            make.width.equalToSuperview()
        }
    }
    
    @objc private func _onInfoButtonTap(_ sender: UIButton) {
        _onInfoTapClosure[sender.tag]()
    }
    
    private func insertSubmitButton(_ widget: SupermarketLoyaltyMembershipSubmissionWidgetViewModel) {
        let button = UIButton(type: .roundedRect)
        button.setTitle(widget.button, for: .normal)
        button.titleLabel?.font = .hf_small_bold()
        button.layer.cornerRadius = 4
        configureButtonEnabled(button,isEnabled: false)
        stack.addArrangedSubview(button)
        button.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalTo(40)
        }
        button.addTarget(self, action: #selector(_onSubmitTap), for: .touchUpInside)
        
        submissionRuleObservable.onDidChange = { [unowned self, unowned button] rule in
            configureButtonEnabled(button,isEnabled: rule.isValid)
        }
    }
    
    @objc private func _onSubmitTap() {
        Task {
            toggleLoading(true)
            await viewModel.submit(inputs: inputViewModels)
            toggleLoading(false)
        }
    }
    
    @MainActor
    private func toggleLoading(_ isShow: Bool) {
        if isShow {
            SVProgressHUD.show()
        } else {
            SVProgressHUD.dismiss()
        }
    }
    
    private func configureButtonEnabled(_ button: UIButton, isEnabled: Bool) {
        button.isEnabled = isEnabled
        if button.isEnabled {
            button.backgroundColor = .hf_happy_100()
            button.setTitleColor(.hf_white(), for: .normal)
        } else {
            button.backgroundColor = .hf_grey_00()
            button.setTitleColor(.hf_grey_40(), for: .normal)
        }
    }
    
    private func insertTncButton(_ widget: SupermarketLoyaltyMembershipSubmissionWidgetViewModel) {
        let label = UILabel()
        label.textAlignment = .center
        label.numberOfLines = 0
        label.lineBreakMode = .byWordWrapping
        
        if let tnc = widget.tnc {
            configureTncFromHtmlString(string: tnc, label: label)
        }
        
        stack.addArrangedSubview(label)
        label.snp.makeConstraints { make in
            make.width.equalToSuperview()
        }
        
        let tap = UITapGestureRecognizer(target: self, action: #selector(_showTnc))
        label.addGestureRecognizer(tap)
        label.isUserInteractionEnabled = true
    }
    
    private func insertMessage(_ widget: SupermarketLoyaltyMembershipMessageWidgetViewModel) {
        let view = StatusMessageView()
        view.configure(type: StatusMessageType(rawValue: widget.state ?? ""),
                       message: widget.message)
        
        stack.addArrangedSubview(view)
        view.snp.makeConstraints { make in
            make.width.equalToSuperview()
        }
    }
    
    private var tncCallbacks = [String: () -> Void]()
    
    private func configureTncFromHtmlString(string: String, label: UILabel) {
        if let mutableAttributeString = try? NSMutableAttributedString(data: string.data(using: .utf8)!, options: [.documentType:NSAttributedString.DocumentType.html,], documentAttributes: nil) {
            mutableAttributeString.setAttributes(
                [
                    .font: UIFont.hf_title_12()!,
                    .foregroundColor: UIColor.hf_grey_80()!
                ],
                range: NSRange(location: 0, length: mutableAttributeString.length)
            )
            configureForTermAndConditions(string: string, mutableAttributeString: mutableAttributeString)
            label.attributedText = mutableAttributeString
        }
    }
    
    private func configureForTermAndConditions(string: String, mutableAttributeString: NSMutableAttributedString) {
        let (url, content) = extractFromATag(string: string, index: 0)
        mutableAttributeString.setAttributes(
            [
                .font: UIFont.hf_micro_bold()!,
                .foregroundColor: UIColor(red: 140 / 255, green: 197 / 255, blue: 64 / 255, alpha: 1)
            ],
            range: (mutableAttributeString.string as NSString).range(of: content)
        )
        tncCallbacks[content] = { [weak self] in
            guard let self else { return }
            guard let url else { return }
            let vc = WebViewController(url: url as NSURL,
                                       title: NSLocalizedString("Terms and Conditions", comment: ""),
                                       screenName: ScreenNameTermsAndConditions,
                                       source: self.screenName,
                                       showRefreshButton: false)
            self.open(viewController: vc, isFullScreen: false)
        }
    }
    
    private func extract(index: Int, string: String, match: NSTextCheckingResult) -> String {
        let startIndex = string.index(string.startIndex, offsetBy: match.range(at: index).lowerBound)
        let endIndex = string.index(string.startIndex, offsetBy: match.range(at: index).upperBound)
        return String(string[startIndex..<endIndex])
    }
    
    private func extractFromATag(string: String, index: Int) -> (url: URL?, content: String) {
        let regex = try! NSRegularExpression(pattern: #"<a.*?>(.*?)<\/a>"#)
        let match = regex.matches(in: string, range: NSRange(location: 0, length: string.count))[index]
        let url = extractLink(string: extract(index: 0, string: string,match: match))
        let content = extract(index: 1, string: string, match: match)
        
        return (url, content)
    }
    
    private func extractLink(string: String) -> URL? {
        let regex = try! NSRegularExpression(pattern: #"<a ?href='(.*)'>"#)
        let match = regex.matches(in: string, range: NSRange(location: 0, length: string.count))[0]
        let startIndex = string.index(string.startIndex, offsetBy: match.range(at: 1).lowerBound)
        let endIndex = string.index(string.startIndex, offsetBy: match.range(at: 1).upperBound)
        let link = string[startIndex..<endIndex]
        return URL(string: String(link))
    }
    
    @objc private func _showTnc(_ tap: UITapGestureRecognizer) {
        let label = tap.view as! UILabel
        let text = (label.attributedText?.string as? NSString)!
        for (key,callback) in tncCallbacks {
            if tap.didTapAttributedTextInLabel(label: label, inRange: text.range(of: key)) {
                callback()
                break
            }
        }
    }
    
    @objc private func shopNowTapped() {
        dismiss(animated: true)
    }
}

extension SupermarketLoyaltyMembershipViewController {
    @objc private func textFieldDidChange(_ textField: UITextField) {
        let text = textField.text ?? ""
        submissionRuleObservable.value.inputs[textField.tag] = text
        inputViewModels[textField.tag].value = text
    }
}
