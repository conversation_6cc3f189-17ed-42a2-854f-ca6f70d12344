//
//  SupermarketLoyaltyMembership.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON>@HappyFresh on 12/02/23.
//  Copyright © 2023 HappyFresh Inc. All rights reserved.
//

import UIKit

final class SupermarketLoyaltyMembershipWidget: StoreWidget<SupermarketLoyaltyMembershipWidgetView, StoreHomeSupermarketLoyaltyMembershipViewModel> {
    
    var onTap: (() -> Void)?
    
    override func createMainCell(at index: Int) -> SupermarketLoyaltyMembershipWidgetView? {
        let cell = super.createMainCell(at: index)
        cell?.onTap = onTap
        return cell
    }
    
    override func sizeForItem(at index: Int) -> CGSize {
        guard let containerWidth = collectionContext?.containerSize.width,
              containerWidth > 0.0 else {
            return .zero
        }
        return CGSize(width: containerWidth, height: UIScreen.main.bounds.width * 0.2 + 16)
    }
}

final class SupermarketLoyaltyMembershipWidgetView: StoreWidgetCell<StoreHomeSupermarketLoyaltyMembershipViewModel> {
    
    var onTap: (() -> Void)?
    
    override var viewModel: StoreHomeSupermarketLoyaltyMembershipViewModel? {
        didSet {
            setupView()
        }
    }
    
    private lazy var membershipImageView: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "hf_img_placeholder_banner"))
        imageView.contentMode = .scaleAspectFit
        imageView.setupShadow(withRadius: 8, opacity: 1, offset: CGSize(width: 0, height: 2))
        imageView.isUserInteractionEnabled = true
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(_onTap))
        imageView.addGestureRecognizer(tapGesture)
        
        return imageView
    }()
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        commonInit()
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        commonInit()
    }
    
    private func commonInit() {
        contentView.addSubview(membershipImageView)
        membershipImageView.snp.makeConstraints {
            $0.leading.trailing.equalToSuperview().inset(16)
            $0.top.equalToSuperview().inset(8)
            $0.bottom.equalToSuperview().offset(-8)
            $0.height.equalTo(UIScreen.main.bounds.width * 0.2)
        }
    }
    
    private func observeNotification() {
        NotificationCenter.default.addObserver(
            forName: .UserInfoUpdated,
            object: nil,
            queue: nil
        ) { [weak self] (notification) in
            self?.setupView()
        }
    }
    
    private func setupView() {
        if let imageUrl = viewModel?.msiMembershipImage,
           let url = URL(string: imageUrl) {
            membershipImageView.add_setImage(with: url)
        }
    }
    
    @objc private func _onTap() {
        onTap?()
    }
}
