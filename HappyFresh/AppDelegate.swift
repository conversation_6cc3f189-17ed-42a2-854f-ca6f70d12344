//
//  AppDelegate.swift
//  HappyFresh
//
//  Created by <PERSON> on 08/07/22.
//  Copyright © 2022 HappyFresh Inc. All rights reserved.
//

import FBSDKCoreKit
import UIKit
import MagicalRecord
import SVProgressHUD
import MoEngageSDK
import MoEngageMessaging

@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate {
    var window: UIWindow?
    @objc var currentAuthorizationFlow: OIDExternalUserAgentSession?
    let deeplinkUtils = DeeplinkUtils.sharedInstance()
    let pushNotificationUtils = PushNotificationUtils.sharedInstance()
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        // IMPORTANT: let core data setup run first
        ConfigService.setupCoreData()
        ConfigService.setupFirebase()
        ConfigService.startSentry()
        ConfigService.setupGoogleServices()

        FirebaseRemoteConfigService.sharedInstance.fetchAndActivate()
        HFPerformance.shared.setup()
        HFPerformance.shared.appStartToFirstScreenTrace?.start()
        
        // allow disk cache of images
        let urlCache = URLCache(memoryCapacity: 4 * 1024 * 1024, diskCapacity: 100 * 1024 * 1024, diskPath: "app_cache")
        URLCache.shared = urlCache
        
        // Tracker
        let properties = ApplicationOpenedProperties()
        properties.source = EventPropertyApplicationOpenedSourceDirect
        App.sharedInstance().tracker().applicationOpenedProperties = properties

        // Setup third party services
        HFAnalytics.shared.setup(withLaunchOptions: launchOptions)
        ConfigService.setupAppsFlyer(with: deeplinkUtils)
        NewsfeedService.shared.requestDataFromServer()
        ConfigService.updateCrashlyticsUser()
        ExperimentService.setup()
        
        AppTracking.shared.applicationFinishLaunch(launchOptions: launchOptions ?? [:])
        Tracker.sharedInstance().identify()
        if let userInfo = launchOptions?[.remoteNotification] as? [AnyHashable:Any] {
            handleRemoteNotification(userInfo: userInfo, tap: true)
        }
        
        ApplicationDelegate.shared.application(application, didFinishLaunchingWithOptions: launchOptions)
        
        ScreenshotService.sharedInstance().listenScreenshotEvent()

        App.sharedInstance().syncCurrenciesRate()

        UNUserNotificationCenter.current().delegate = self
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            if (settings.authorizationStatus == .authorized) {
                DispatchQueue.main.async {
                    application.registerForRemoteNotifications()
                }
            }
        }

        // IMPORTANT: set language must come first before setup view
        let appLanguage = LanguageService.sharedInstance.language
        let language = LanguageService.languageCode(for: appLanguage)
        Bundle.setLanguage(language)
        
        UITabBarItem.appearance().setTitleTextAttributes([
            NSAttributedString.Key.font: UIFont.hf_title_12()!
        ], for: .normal)
        self.configureNavigationBar()
        SVProgressHUD.setDefaultMaskType(.black)
        
        self.setupRootViewController()
        
        return true
    }
    
    func applicationWillEnterForeground(_ application: UIApplication) {
        let properties = ApplicationOpenedProperties()
        properties.fromBackground = true
        properties.source = EventPropertyApplicationOpenedSourceDirect
        App.sharedInstance().tracker().applicationOpenedProperties = properties
        AppTracking.shared.applicationOpened(fromBackground: true)
        
        // Fix bug: [splash] can’t key in invitation code unless restart the app
        // http://www.acnenomor.com/4003697p1/hittest-fires-when-uikeyboard-is-tapped
        // The keyboard sometimes disables interaction when the app enters the
        // background due to an iOS bug. This brings it back to normal.
        for testWindow in UIApplication.shared.windows {
            if !testWindow.isOpaque && NSStringFromClass(type(of: testWindow).self).hasPrefix("UIText") {
                let wasHidden = testWindow.isHidden
                testWindow.isHidden = true
                if !wasHidden {
                    testWindow.isHidden = false
                }
                break
            }
        }
        
        NotificationCenter.default.post(name: NSNotification.Name.OrderRefresh, object: nil, userInfo: nil)
        NotificationCenter.default.post(name: NSNotification.Name.ShowForceUpdate, object: nil)
        NotificationCenter.default.post(name: NSNotification.Name.BackgroundForeground, object: nil)
    }
    
    func applicationDidBecomeActive(_ application: UIApplication) {
        Tracker.sharedInstance().af_trackAppLaunch()
        
        checkAppStoreVersion()
        
        NotificationService.shared.getAllDeliveredNotifications() { notifications in
            if notifications.count == 0 {
                UIApplication.shared.applicationIconBadgeNumber = 0
            }
            NotificationService.shared.clearBrazeUserNotification()
        }

        NewsfeedService.shared.requestDataFromServer()
        UserService.shared.getUserData()
    }
    
    func applicationDidEnterBackground(_ application: UIApplication) {
        Tracker.sharedInstance().trackApplicationBackgrounded()
    }
    
    func applicationWillTerminate(_ application: UIApplication) {
        MagicalRecord.cleanUp()
    }
    
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return .portrait
    }
    
    
    // MARK: - Push Notification
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        AppsFlyerLib.shared().registerUninstall(deviceToken)
        MoEngageSDKMessaging.sharedInstance.setPushToken(deviceToken)
    }
    
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        PushNotificationUtils.sharedInstance().handleSilentPushAlert(userInfo: userInfo as! [String: Any])
        handleRemoteNotification(userInfo: userInfo)
    }

    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        MoEngageSDKMessaging.sharedInstance.didFailToRegisterForPush()
    }
    
    // MARK: - Deeplink
    /* Deeplink callback for new user */
    func application(_ app: UIApplication,
                     open url: URL,
                     options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
      // Sends the URL to the current authorization flow (if any) which will
      // process it if it relates to an authorization response.
      if let authorizationFlow = self.currentAuthorizationFlow,
                                 authorizationFlow.resumeExternalUserAgentFlow(with: url) {
        self.currentAuthorizationFlow = nil
        return true
      }
        
        App.sharedInstance().tracker().applicationOpenedProperties?.referringApplication = options[UIApplication.OpenURLOptionsKey.sourceApplication] as? String
        
        if ApplicationDelegate.shared.application(app, open: url, sourceApplication: options[UIApplication.OpenURLOptionsKey.sourceApplication] as? String, annotation: options[UIApplication.OpenURLOptionsKey.annotation]) {
            return true
        }
        
        if !url.queryDictionary.isEmpty && (url.scheme == Bundle.main.urlScheme()) {
            var params = url.queryDictionary
            if let param = params["source"] as? String {
                params["source"] = params["source"] != nil ? param : TrackingDeeplinkSourceAppsflyer
            }
            deeplinkUtils.processDeeplink(withParams: params)
        }
        return true
    }
    
    /* Deeplink callback for existing user */
    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        guard let webpageURL = userActivity.webpageURL,
              let host = webpageURL.host else { return false }
        let appsFlyerOnelinkDomain = Bundle.main.appsFlyerOnelinkDomain()
        let clickRecordingDomain = Bundle.main.marketingClickRecordingDomain()
        
        if (host == appsFlyerOnelinkDomain) || (host == clickRecordingDomain) {
            AppsFlyerLib.shared().continue(userActivity)
            return true
        }
        
        if deeplinkUtils.isValidUniversalLink(host: host) {
            deeplinkUtils.handleInternalUniversalLink(url: webpageURL)
        }
        
        return true
    }
}
