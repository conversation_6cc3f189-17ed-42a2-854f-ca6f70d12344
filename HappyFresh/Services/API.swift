//
//  API.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 21/02/20.
//  Copyright © 2020 HappyFresh Inc. All rights reserved.
//

import Foundation
import ReactiveSwift
import Result
import AppsFlyerLib

extension API {
    static func header(orderToken: String? = nil) -> [String: String] {
        var header: [String: String?] = [
            "X-Happy-Client-Type": "ios"
        ]

        if let identityAccessToken = AuthService.sharedInstance().authState?.lastTokenResponse?.accessToken {
            header["Authorization"] = "Bearer \(identityAccessToken)"
        } else if let userToken = UserService.shared.user?.token {
            header["X-Spree-Order-Token"] = userToken
        } else {
            header["X-Spree-Client-Token"] = ClientToken
        }

        if let orderToken {
            header["X-Spree-Order-Token"] = orderToken
        }

        if let _ = UserService.shared.user?.isCorporate {
            header["X-Spree-User-Type"] = "hc"
        }


        let app = App.sharedInstance()
        let appLanguage = LanguageService.sharedInstance.language
        let language = LanguageService.languageCode(for: appLanguage).uppercased()
        let countryCode = app.countryCode.uppercased()
        let clientVersion = app.version
        let clientBuild = app.buildNumber

        header["X-Happy-Client-Version"] = clientVersion
        header["X-Happy-Client-Build"] = clientBuild
        header["Locale"] = language
        header["Country"] = countryCode
        header["X-Country-Code"] = countryCode
        header["X-DEVICE-ID"] = app.deviceId
        header["X-AF-ID"] = AppsFlyerLib.shared().getAppsFlyerUID()
        header["X-FP-VISITOR-ID"] = HFUserDefault.sharedInstance().visitorID

        return header.compactMapValues { $0 }
    }

    func updateReplacementPreferences(orderNumber: String,
                                      orderToken: String,
                                      lineItemID: NSNumber,
                                      params: [String : Any],
                                      stockLocationID: Int,
                                      fetchSuccess: @escaping (ReplacementPreference) -> Void,
                                      fetchFailure: @escaping ([String:Any]?) -> Void) {
        let url = "/api/orders/\(orderNumber)/line_items/\(lineItemID)/replacement_preferences"
        self.manager(withOrderToken: orderToken).post(url, parameters: params, success: { (operation, responseObject) in
            guard let data = responseObject as? [String: Any] else { return }

            if let replacementPreference = ReplacementPreference.createOrUpdate(dictionary: data, stockLocationID: stockLocationID) {
                NSManagedObjectContext.mr_default().mr_saveToPersistentStore { success, error in
                    if success {
                        fetchSuccess(replacementPreference)
                    }
                }
            }
        }) { (operation, error) in
            fetchFailure(operation?.responseObject as? [String:Any])
        }
    }

    func removeReplacementPreference(
        orderNumber: String,
        orderToken: String,
        lineItemID: NSNumber,
        success: (() -> Void)?,
        failure: ((String?) -> Void)?
    ) {
        let url = "/api/orders/\(orderNumber)/line_items/\(lineItemID)/replacement_preferences"

        manager(withOrderToken: orderToken).delete(url, parameters: nil, success: { (operation, responseObject) in
            success?()
        }) { (operation, error) in
            failure?(operation?.errorMessage())
        }
    }

    func getAvailableSlots(orderNumber: String, orderToken: String, stockLocationID: Int, location: CLLocation?, date: Date, completion: @escaping ([Slot], String?, String?) -> Void) {
        
        var url = "/api/slots/available?order_number=\(orderNumber)&stock_location_id=\(stockLocationID)&date=\(date.toDisplayString(with: "yyyy-MM-dd"))"

        if let coordinate = location?.coordinate {
            url = url + "lat=\(coordinate.latitude)&long=\(coordinate.longitude)"
        }

        self.manager(withOrderToken: orderToken).get(url, parameters: nil, success: { (operation, responseObject) in
            guard let responseDictionary = responseObject as? [String: AnyObject] else {
                completion([], nil, nil)
                return
            }
            guard let slotsArray = responseDictionary["slots"] as? [[String: AnyObject]] else {
                completion([], nil, nil)
                return
            }

            let slots = Slot.slots(from: slotsArray)

            NSManagedObjectContext.mr_default().mr_saveToPersistentStore { _, _ in
                completion(slots, responseDictionary["operation_message"] as? String, nil)
            }
        }) { (operation, error) in
            completion([], nil, error.localizedDescription)
        }
    }

    func getStockLocationsNearby(coordinate: CLLocationCoordinate2D, stockLocationID: Int? = nil, zipcode: String? = nil, sublocality: String? = nil, perPage: Int? = nil, completion: ((_ stockLocations: [StockLocation], _ errorMessage: String?) -> Void)?) {
        var url = String(format: "/api/v2/stock_locations/nearby?lat=%@&lon=%@&get_next_available_slot=1&skip_reverse_geocode=true", "\(coordinate.latitude)", "\(coordinate.longitude)")

        if let stockLocationID = stockLocationID {
            url.append("&stock_location_id=\(stockLocationID)")
        }

        if let zipcode = zipcode {
            url.append("&zipcode=\(zipcode)")
        }

        if let sublocality = sublocality {
            url.append("&sublocality=\(sublocality)")
        }

        if let perPage = perPage {
            url.append("&per_page=\(perPage)")
        }
        
        if ApptimizeService.isHfsPlusChoiceEnabled {
            url.append("&show_hfs_plus=\(true)")
        }

        manager.get(url, parameters: nil, success: { (operation, responseObject) in
            guard let responseDictionary = responseObject as? [String: AnyObject] else {
                completion?([], operation.errorMessage())
                return
            }

            guard let stockLocationsArray = responseDictionary["stock_locations"] as? [[String: AnyObject]] else {
                completion?([], operation.errorMessage())
                return
            }

            guard let stockLocations = StockLocation.array(fromJSONArray: stockLocationsArray) as? [StockLocation] else {
                completion?([], operation.errorMessage())
                return
            }

            completion?(stockLocations, nil)
        }) { (operation, error) in
            completion?([], operation?.errorMessage())
        }
    }
    
    func fetchAvailableSlotDates(
        stockLocationID: Int,
        completion: @escaping ([SlotDate]) -> Void,
        failure: @escaping (String?) -> Void
    ) {
        let url = "/api/v2/slots/available_dates?stock_location_id=\(stockLocationID)"

        manager.get(url, parameters: nil, success: { (operation, responseObject) in
            if let response = responseObject as? [String: Any],
               let availableDates = response["available_dates"] as? [[String: Any]] {
                do {
                    let availableDatesData = try JSONSerialization.data(withJSONObject: availableDates)
                    let slotDates = try JSONDecoder().decode([SlotDate].self, from: availableDatesData)
                    completion(slotDates)
                } catch {
                    failure(error.localizedDescription)
                }
            } else {
                failure(operation.errorMessage())
            }
        }) { (operation, error) in
            failure(operation?.errorMessage())
        }
    }

    @available(iOS 13.0, *)
    func login(withAppleID userInfo: AppleIDUserInfo,
               success: @escaping (User?, StockLocation?, String?, Bool) -> Void,
               failure: @escaping (String?, String?) -> Void) {
        let url = "/api/login_apple"
        var params = ["user_identifier": userInfo.userIdentifier]

        if let identityToken = userInfo.identityToken {
            params["identity_token"] = String(decoding: identityToken, as: UTF8.self)
        }

        if let authorizationCode = userInfo.authorizationCode {
            params["authorization_code"] = String(decoding: authorizationCode, as: UTF8.self)
        }

        if let firstName = userInfo.firstName {
            params["first_name"] = firstName
        } else if let email = userInfo.email {
            params["first_name"] = email
        }

        if let lastName = userInfo.lastName {
            params["last_name"] = lastName
        }

        if let email = userInfo.email {
            params["email"] = email
        }
        
        if let referralID = HFUserDefault.sharedInstance().referralCustomerID {
            params["referrer_id"] = referralID
        }
        
        if let referralChannel = HFUserDefault.sharedInstance().referralChannel {
            params["referral_channel"] = referralChannel
        }

        manager.post(url, parameters: params, success: { (operation, responseObject) in
            guard let responseDictionary = responseObject as? [String: AnyObject] else {
                failure(operation.errorMessage(), operation.errorType())
                return
            }

            let user = User(from: responseDictionary)

            if let userID = user.userID?.intValue, userID > 0 {
                let language = responseDictionary["language"] as? String
                let isExistingUser = (operation.response?.statusCode ?? 0) == 200 // New user will return 201 status code
                
                HFUserDefault.sharedInstance().referralCustomerID = nil
                HFUserDefault.sharedInstance().referralChannel = nil
                var lastStockLocation: StockLocation?

                if let lastStockLocationDicionary = responseDictionary["last_stock_location"] as? [String: AnyObject] {
                    lastStockLocation = StockLocation(from: lastStockLocationDicionary)
                }

                NSManagedObjectContext.mr_default().mr_saveToPersistentStore { _, _ in
                    success(user, lastStockLocation, language, isExistingUser)
                }
            } else {
                failure(operation.errorMessage(), operation.errorType())
            }
        }) { (operation, error) in
            failure(operation?.errorMessage(), operation?.errorType())
        }
    }
    
    func getUserData(completion: @escaping (User?) -> Void, failure: @escaping (String?, String?) -> Void) {
        guard let currentUser = App.sharedInstance().user,
              let userID = App.sharedInstance().user?.userID else {
            completion(nil)
            return
        }
        manager.get("api/users/\(userID)", parameters: nil, success: { (operation, responseObject) in
            guard let responseDictionary = responseObject as? [String: AnyObject] else {
                completion(nil)
                return
            }

            let updatedUser = User(from: responseDictionary)
            UserService.shared.user = updatedUser
            
            if currentUser.isSubscriber != updatedUser.isSubscriber {
                NotificationCenter.default.post(name: .SubscriberStatusChanged, object: nil)
            }
            
            if currentUser.mySuperindo?.user?.userID != updatedUser.mySuperindo?.user?.userID {
                NotificationCenter.default.post(name: .UserInfoUpdated, object: nil)
            }

            if let userID = updatedUser.userID?.intValue, userID > 0 {
                NSManagedObjectContext.mr_default().mr_saveToPersistentStore { _, _ in
                    completion(updatedUser)
                    Tracker.sharedInstance().identifyIsSubscriber(isSubscriber: updatedUser.isSubscriber)
                }
            } else {
                completion(nil)
            }
        }) { (operation, error) in
            failure(operation?.errorMessage(), operation?.errorType())
        }
    }
    
    @objc
    func shareProduct(with productID: NSNumber,
                      stockLocationID: NSNumber,
                      completion: @escaping (String?) -> Void,
                      failure: @escaping (Error?) -> Void) {
        let url = "/api/stock_locations/\(stockLocationID)/products/\(productID)/share"
        manager.get(url, parameters: nil) { (_, responseObject) in
            guard let responseObject = responseObject as? [String: AnyObject] else {
                failure(nil)
                return
            }
            completion(responseObject["sharing_text"] as? String)
        } failure: { (_, error) in
            failure(error)
        }
    }
    
    func getLoyaltyData(completion: @escaping (Loyalty?) -> Void, failure: @escaping (Error?) -> Void) {
        guard let userID = App.sharedInstance().user?.userID else {
            completion(nil)
            return
        }
        
        manager.get("api/loyalty/users/\(userID)/membership", parameters: nil) { (operation, responseObject) in
            guard let responseDictionary = responseObject as? [String: AnyObject] else {
                completion(nil)
                return
            }
            
            let loyalty = Loyalty(responseDictionary)
            completion(loyalty)
        } failure: { (operation, error) in
            failure(error)
        }
    }
    
    func getLoyaltyData() -> SignalProducer<(Loyalty?), Error> {
        SignalProducer { [weak self] observer, _ in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            self.getLoyaltyData() { loyalty in
                observer.send(value: loyalty)
                observer.sendCompleted()
            } failure: { error in
                if let error = error {
                    observer.send(error: error)
                }
                observer.sendCompleted()
            }
        }
    }

    @objc
    func getStockLocationsWithSupplierID(_ supplierID: Int,
                                         location: CLLocation,
                                         limit: Bool = false,
                                         completion: @escaping ([StockLocation], Subdistrict?, String?) -> Void) {
        let coordinate = location.coordinate

        var url = "/api/suppliers/\(supplierID)/stock_location?lat=\(coordinate.latitude)&lon=\(coordinate.longitude)\(limit ? "" : "&limit=false")"

        if ApptimizeService.isHfsPlusChoiceEnabled {
            url.append("&show_hfs_plus=\(true)")
        }
        
        manager.get(url, parameters: nil) { (operation, responseObject) in
            guard let responseDictionary = responseObject as? [String: AnyObject],
                  let stockLocationsArray = responseDictionary["stock_locations"] as? [[String: AnyObject]],
                  let stockLocations = StockLocation.array(fromJSONArray: stockLocationsArray) as? [StockLocation] else {
                completion([], nil, operation.errorMessage())
                return
            }

            var subdistrict: Subdistrict?

            if let lastStockLocationDicitionary = stockLocationsArray.last,
               let subdistrictDictionary = lastStockLocationDicitionary["sub_district"] as? [String: AnyObject] {
                subdistrict = Subdistrict(from: subdistrictDictionary)
            }

            completion(stockLocations, subdistrict, nil)
        } failure: { (operation, error) in
            completion([], nil, operation?.errorMessage())
        }
    }

    @objc
    func getNearestStockLocationWithSupplierID(_ supplierID: Int,
                                               location: CLLocation,
                                               completion: @escaping (StockLocation?, Subdistrict?, String?) -> Void) {
        getStockLocationsWithSupplierID(supplierID, location: location, limit: true) { (stockLocations, subdisctrict, errorMessage) in
            completion(stockLocations.first, subdisctrict, errorMessage)
        }
    }
    
    func getBasketConfig(
        orderNumber: String,
        orderToken: String,
        completion: @escaping (BasketConfig?) -> Void,
        failure: @escaping (Error?) -> Void
    ) {
        let url = "/api/config/basket"
        let params = [
            "order_number": orderNumber,
            "payment_promo_banner": "true"
        ] as [String : Any]

        manager(withOrderToken: orderToken).get(url, parameters: params) { (_, responseObject) in
            guard let responseObject = responseObject as? [String: AnyObject] else {
                failure(nil)
                return
            }
            let response = BasketConfig(dictionary: responseObject)
            completion(response)
        } failure: { (_, error) in
            failure(error)
        }
    }
    
    func getLocationGuidesConfig(
        completion: @escaping (LocationGuides) -> Void,
        failure: @escaping (Error?) -> Void
    ) {
        let url = "/api/config/location_guides"
        manager.get(url, parameters: nil) { _, responseObject in
            guard let responseObject = responseObject as? [String: AnyObject] else {
                failure(nil)
                return
            }
            let response = LocationGuides(dictionary: responseObject)
            completion(response)
        } failure: { _, error in
            failure(error)
        }
    }
    
    /// Reactive version of refreshOrder.
    func refreshOrder(_ order: Order, keepOosItems: Bool = false) -> SignalProducer<Order?, Error> {
        SignalProducer { [weak self] (observer, _) in
            guard let self = self else {
                observer.sendCompleted()
                return
            }

            self.refreshOrder(order, keepOosItems: keepOosItems) { order, error in
                if let error = error {
                    observer.send(error: error)
                    observer.sendCompleted()
                    return
                }

                observer.send(value: order)
                observer.sendCompleted()
            }
        }
    }
    
    /// Reactive version of getBasketConfig.
    func getBasketConfig(orderNumber: String, orderToken: String) -> SignalProducer<BasketConfig?, Error> {
        SignalProducer { [weak self] (observer, _) in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            self.getBasketConfig(orderNumber: orderNumber, orderToken: orderToken) { (basketConfig) in
                observer.send(value: basketConfig)
                observer.sendCompleted()
            } failure: { (error) in
                if let error = error {
                    observer.send(error: error)
                }
                observer.sendCompleted()
            }
        }
    }
    
    /// Reactive version of searchCampaign.
    func searchCampaign(forProductId productId: Int, promotionId: Int = 0, location: CLLocation) -> SignalProducer<CampaignDetail?, Error> {
        SignalProducer { [weak self] (observer, _) in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            self.searchCampaign(forProductId: productId, promotionId: promotionId, nearLocation: location) { (products, stockLocations, subdistrict) in
                let products = products?.compactMap({ $0 as? Product }) ?? []
                let stockLocations = stockLocations?.compactMap({ $0 as? StockLocation }) ?? []
                
                observer.send(value: CampaignDetail(products: products, stockLocations: stockLocations, subdistrict: subdistrict))
            } failure: { (error) in
                if let error = error {
                    observer.send(error: error)
                }
                observer.sendCompleted()
            }

        }
    }
    
    func checkFleetUtilizationAvailable(stocklocationID: NSNumber,
                                        completion: @escaping (StoreFleetUtilization?) -> Void) {
        let url = "/api/stock_locations/\(stocklocationID)/fleet_utilization_availability"
        API.sharedInstance()
            .managerWithFullfilmentToken()
            .get(url, parameters: nil) { operation, response in
                guard let json = response as? [String : Any] else {
                    completion(nil)
                    return
                }

                let storeFleeUtilization = StoreFleetUtilization(json: json)
                completion(storeFleeUtilization)
            } failure: { operation, error in
                completion(nil)
            }
    }
    
    func checkVoucherEligibility(orderNumber: String, _ newsfeedCards: [Newsfeed]) -> SignalProducer<([Voucher], [String : Any]?), Error> {
        SignalProducer { [weak self] (observer, _) in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            self.checkVoucherEligibility(orderNumber: orderNumber, newsfeedCards: newsfeedCards) { vouchers, trackData, error in
                guard let error = error else {
                    observer.send(value: (vouchers, trackData))
                    return
                }
                observer.send(error: error)
                observer.sendCompleted()
            }
        }
    }
    private func checkVoucherEligibility(orderNumber: String,
                                         newsfeedCards: [Newsfeed],
                                         completion: @escaping ([Voucher], [String : Any]?, Error?) -> Void) {
        let url = "/api/promotions/vouchers?order_number=\(orderNumber)"
        let promotions = newsfeedCards.map({
            ["voucher_code": $0.voucherCode,
             "promotion_id": $0.promotionID]
        })
        manager.post(url, parameters: ["promotions": promotions]) { (_, responseObject) in
            if let responseObject = responseObject as? [String : Any] {
                let voucherPromotions = responseObject["promotions"] as? [[String : Any]] ?? []
                let trackData = responseObject["track_data"] as? [String : Any]
                let vouchers = voucherPromotions.compactMap ({ dictionary -> Voucher? in
                    guard let voucherCode = dictionary["voucher_code"] as? String,
                          let cardIndex = newsfeedCards.firstIndex(where: {
                            $0.voucherCode?.elementsEqual(voucherCode) ?? false
                          }) else { return nil }
                    return Voucher(dictionary, card: newsfeedCards[cardIndex])
                })
                
                completion(vouchers.sorted(by: { $0.priority < $1.priority }), trackData, nil)
            }
        } failure: { (_, error) in
            completion([], nil, error)
        }
    }
    
    func updateReplacementsPreferencesVariants(order: Order,
                                                fetchSuccess: @escaping () -> Void) {
        guard let orderNumber = order.number,
              let orderToken = order.token else { return }
        manager(withOrderToken: orderToken)
            .get("/api/v3/orders/\(orderNumber)/replacements_preferences_variants", parameters: nil, success: { (_, responseObject) in
                guard let response = responseObject as? [String: Any],
                      let objectsLineItems = response["line_items"] as? [[String: Any]] else { return }
                for objectLineItem in objectsLineItems {
                    if let lineItemID = objectLineItem["id"] as? NSNumber,
                       let lineItem = order.lineItems?.first(where: { ($0 as? LineItem)?.lineItemID == lineItemID }) as? LineItem,
                       let replacementObject = objectLineItem["replacement_preference"] as? [String : Any],
                       let replacementVariant = replacementObject["replacement_variant"] as? [String : Any] {
                        lineItem.replacementPreference?.replacementVariant?.update(data: replacementVariant)
                    }
                }
                NSManagedObjectContext.mr_default().mr_saveToPersistentStore { _, _ in
                    fetchSuccess()
                }
                
            }, failure: nil)
    }
    
    func getPastItems(stockLocation: StockLocation,
                      page: Int,
                      perPage: Int,
                      sortQuery: String,
                      completion: @escaping ([StockItem]?, Int?, Int?, Int?, String?) -> Void) {
        guard let stockLocationID = stockLocation.stockLocationID else {
            completion(nil, nil, nil, nil, nil)
            return
        }
        
        let url = "/api/orders/past_items?stock_location_id=\(stockLocationID)&page=\(page)&per_page=\(perPage)&q[s]=\(sortQuery)"
        manager.get(url, parameters: nil) { operation, responseObject in
            guard let response = responseObject as? [String: Any],
                  let stockItemsDicts = response["stock_items"] as? [[String: Any]] else {
                completion(nil, nil, nil, nil, nil)
                return
            }
            let stockItems = stockItemsDicts.compactMap({ StockItem(from: $0, stockLocation: stockLocation) })
            NSManagedObjectContext.mr_default().mr_saveToPersistentStore { _, _ in
                completion(stockItems,
                           response["current_page"] as? Int,
                           response["pages"] as? Int,
                           response["total_count"] as? Int,
                           nil)
            }
        } failure: { operation, error in
            completion(nil, nil, nil, nil, operation?.errorMessage())
        }
    }
    
    /// Get payment promotions
    /// - Parameters:
    ///   - orderNumber: Order number
    ///   - completion: Completion handler (`promotions`, `trackData`)
    ///   - failure: Error handler (`error`)
    func getPaymentPromotions(
        orderNumber: String,
        paymentMethodInfo: [String: Any]? = nil,
        completion: @escaping ((_ promotions: [PaymentPromotion], _ trackData: [String: Any]) -> Void),
        failure: @escaping  ((_ error: Error) -> Void)
    ) {
        let url = "/api/promotions/payment_methods"
        let parameters: [String: Any] = ["order_number": orderNumber].deepMerge(paymentMethodInfo ?? [:])
        manager.get(url, parameters: parameters) { operation, responseObject in
            let responseObject = responseObject as? [String: Any] ?? [:]
            let promotions = responseObject["promotions"] as? [[String: Any]] ?? []
            let trackData = responseObject["track_data"] as? [String: Any] ?? [:]
            
            completion(
                PaymentPromotion.arrayFromJson(promotions),
                trackData)
        } failure: { operation, error in
            failure(error)
        }
    }
    
    func getPaymentPromotions(
        orderNumber: String,
        paymentMethodInfo: [String: Any]? = nil
    ) -> SignalProducer<([PaymentPromotion], [String: Any]), Error> {
        SignalProducer { [weak self] observer, _ in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            
            self.getPaymentPromotions(orderNumber: orderNumber, paymentMethodInfo: paymentMethodInfo) { promotions, trackEvent in
                observer.send(value: (promotions, trackEvent))
                observer.sendCompleted()
            } failure: { error in
                observer.send(error: error)
            }
        }
    }
    
    func updatePromotionCheckout(
        with order: Order,
        couponCode: String?,
        paymentPromotionId: Int?,
        success: @escaping ((
            _ order: Order,
            _ submissionMessage: String?
        ) -> Void),
        failure: @escaping ((
            _ operation: AFHTTPRequestOperation?,
            _ error: Error
        ) -> Void)
    ) {
        guard let orderNumber = order.number else { return }
        let url = "/api/v3/checkouts/\(orderNumber)/voucher"
        var orderParams: [String: Any] = [:]

        if let couponCode = couponCode {
            orderParams["coupon_code"] = couponCode
        }
        
        if let paymentPromotionId = paymentPromotionId {
            orderParams["payment_promotion_id"] = paymentPromotionId
        }
        
        manager.patch(url, parameters: ["order": orderParams], success: { operation, response in
            guard let json = response as? [String: Any] else {
                failure(operation, NSError())
                return
            }

            order.showVoucherErrorMessageIfExists(updatedOrderJSON: json)
            order.update(data: json)

            let submissionMessage = json["voucher_error_message"] as? String

            NSManagedObjectContext.mr_default().mr_saveToPersistentStore { _, _ in
                success(order, submissionMessage)
            }
        }, failure: failure)
    }
    
    func getPromotionsSaving(
        promotionIds: [Int],
        orderNumber: String,
        success: @escaping ((_ promotionsSaving: [PromotionSaving]) -> Void),
        failure: @escaping ((_ operation: AFHTTPRequestOperation?, _ error: Error) -> Void)
    ) {
        let url = "/api/promotions/savings"
        let params: [String: Any] = [
            "promotion_ids": promotionIds,
            "order_number": orderNumber
        ]
        
        manager.post(url, parameters: params, success: { _, response in
            let response = response as? [String: Any] ?? [:]
            let promotionsResponse = response["promotions"] as? [[String: Any]] ?? []
            let promotionsSaving = promotionsResponse.compactMap({ PromotionSaving(fromJson: $0) })
            
            success(promotionsSaving)
        }, failure: failure)
    }
    
    func getPromotionsSaving(
        promotionIds: [Int],
        orderNumber: String
    ) -> SignalProducer<[PromotionSaving], Error> {
        SignalProducer { [weak self] observer, _ in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            
            self.getPromotionsSaving(promotionIds: promotionIds, orderNumber: orderNumber) { promotionSavings in
                observer.send(value: promotionSavings)
                observer.sendCompleted()
            } failure: { _, error in
                observer.send(error: error)
            }
        }
    }
    
    func getRefundVoucher(
        completion: @escaping ((_ refundVoucher: RefundVoucher?) -> Void),
        failure: @escaping  ((_ error: Error) -> Void)
    ) {
        let url = "/api/profile/refund_balance"
        manager.get(url, parameters: nil) { _, responseObject in
            let responseObject = responseObject as? [String: Any] ?? [:]
            completion(RefundVoucher(fromJson: responseObject))
        } failure: { _, error in
            failure(error)
        }
    }
    
    func getRefundVoucher() -> SignalProducer<(RefundVoucher?), Error> {
        SignalProducer { [weak self] observer, _ in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            self.getRefundVoucher() { refundVoucher in
                observer.send(value: refundVoucher)
                observer.sendCompleted()
            } failure: { error in
                observer.send(error: error)
            }
        }
    }
    
    func getAddress(type: String) -> SignalProducer<([Address]), Error> {
        SignalProducer { [weak self] observer, _ in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            self.getAddressesWithType(type, success: { _, responseObject in
                guard let addresses = responseObject as? [Address] else {
                    observer.sendCompleted()
                    return
                }
                observer.send(value: addresses)
                observer.sendCompleted()
            }, failure: { _, error in
                guard let err = error else {
                    observer.sendCompleted()
                    return
                }
                observer.send(error: err)
            })
        }
    }
    
    func setOrderAddress(order: Order, addressId: Int, skipRedeem: Bool) -> SignalProducer<(Order?, Error?, HFAlert?, Bool), Error> {
        SignalProducer { [weak self] observer, _ in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            self.setOrderAddress(order: order, addressID: addressId, skipRedeem: skipRedeem) { order, error, alert, notEnoughQuantity in
                observer.send(value: (order, error, alert, notEnoughQuantity))
                observer.sendCompleted()
            }
        }
    }
    
    // MARK: request to GoogleDocs
    
    private var paramWaitList: [String: String] {
        ["key": SubscriptionWaitListKey,
         "majorDimension":"COLUMNS"]
    }
    
    private func getSheetsRemoteConfig(type: String, variation: String, completion: @escaping (([String]) -> Void)) {
        let language = LanguageService.sharedInstance.language.code
        let url = "\(SpreadSheetId)/values/\(type)_\(language == AppLanguage.Melayu.code ? "my" : language)_\(variation)"
        
        googleSheetManager.get(url, parameters: paramWaitList) { operation, responseObject in
            guard let responseDictionary = responseObject as? [String: Any],
            let values = responseDictionary["values"] as? [[String]] else {
                completion([])
                return
            }
            
            completion(values.first ?? [])
        } failure: { operation, err in
            completion([])
        }
    }
    
    private func getSheetsRemotePrice(type: String, variation: String, completion: @escaping ([String]) -> Void) {
        let orderCountry = App.sharedInstance().order?.country?.isoName ?? "id"
        let url = "\(SpreadSheetId)/values/price_\(type)_\(orderCountry)_\(variation)"
        
        googleSheetManager.get(url, parameters: paramWaitList) { operation, responseObject in
            guard let responseDictionary = responseObject as? [String: Any],
            let values = responseDictionary["values"] as? [[String]] else {
                completion([])
                return
            }
            
            let value = orderCountry.lowercased() == "id" ? values.first?.map({ price in
                price.replacingOccurrences(of: "k", with: "rb")
            }) : values.first
            
            completion(value ?? [])
        } failure: { operation, err in
            completion([])
        }
    }
    
    public func getSheetsRemoteConfig(type: SubscriptionConfigType, variation: String) -> SignalProducer<[String], NoError> {
        SignalProducer { [weak self] observer, time in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            self.getSheetsRemoteConfig(type: type.rawValue, variation: variation) { pages in
                observer.send(value: pages)
                observer.sendCompleted()
            }
        }
    }
    
    public func getSheetsRemotePrice(type: SubscriptionConfigType, variation: String) -> SignalProducer<[String], NoError> {
        SignalProducer { [weak self] observer, _ in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            self.getSheetsRemotePrice(type: type.rawValue, variation: variation) { prices in
                observer.send(value: prices)
                observer.sendCompleted()
            }
        }
    }
    
    private func getSavedItems(stockLocation: StockLocation, completion: @escaping (SavedItems) -> Void, error: @escaping (Error) -> Void) {
        let url = "/api/stock_locations/\(stockLocation.stockLocationID ?? 0)/products/saved_products"
        manager.get(url, parameters: nil) { operation, responseObject in
            guard let responseDictionary = responseObject as? [String: AnyObject] else {
                completion(SavedItems())
                return
            }
            
            guard let availableArr = responseDictionary["available"] as? [Any],
                  let unAvailableArr = responseDictionary["unavailable"] as? [Any] else {
                      completion(SavedItems())
                      return
                  }
            
            var savedItems = SavedItems()
            
            if let availableProducts = Product.array(fromJSONArray: availableArr, stockLocation: stockLocation) as? [Product] {
                let avaiPredicate = NSPredicate(format: "(variant.product IN %@) AND stockLocation == %@", availableProducts, stockLocation)
                let avaiStockItems = SortHelper.sortStockItems(with: avaiPredicate, products: availableProducts) ?? []
                savedItems.availables.append(contentsOf: avaiStockItems)
            }
            
            unAvailableArr.forEach { dict in
                if let dict = dict as? [String: Any] {
                    savedItems.unAvailables.append(UnavailableItem(dict: dict))
                }
            }
            completion(savedItems)
        } failure: { operation, err in
            error(err)
        }
        
    }

    public func getSavedItems(stockLocation: StockLocation) -> SignalProducer<SavedItems, Error> {
        SignalProducer { [weak self] observer, _ in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            self.getSavedItems(stockLocation: stockLocation) { savedItems in
                observer.send(value: savedItems)
                observer.sendCompleted()
            } error: { error in
                
            }
        }
    }
    
    public func getSubscriptionBenefits() -> SignalProducer<(SubscriptionLandingResponse?, String?), NoError> {
        SignalProducer { [weak self] observer, _ in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            self.getSubscriptionBenefits { listBenefit in
                observer.send(value: (listBenefit, nil))
                observer.sendCompleted()
            } error: { err, errorMessage in
                observer.send(value: (nil, errorMessage ?? err.localizedDescription))
            }
        }
    }
    
    private func getSubscriptionBenefits(completion: @escaping (SubscriptionLandingResponse?) -> Void,
                                         error: @escaping (Error, String?) -> Void) {
        let url = "/api/subscriptions/landing_page"
        
        manager.get(url, parameters: nil) { operation, responseObject in
            guard let responseDictionary = responseObject as? [String: Any] else {
                completion(nil)
                return
            }
            
            let decoder = JSONDecoder()
            if let data = try? JSONSerialization.data(withJSONObject: responseDictionary,
                                                      options: .fragmentsAllowed),
                let model = try? decoder.decode(SubscriptionLandingResponse.self, from: data) {
                completion(model)
            } else {
                completion(nil)
            }
            
        } failure: { operation, err in
            error(err, operation?.errorMessage())
        }
    }
    
    func verifyInAppPurchaseReceipt(receiptData: String, completion: @escaping (Bool) -> Void) {
        let url = "/api/apple_webhook_receiver/subscription/verify_receipt"
        
        manager.post(url, parameters: ["receipt-data": receiptData]) { operation, responseObject in
            completion(true)
            
        } failure: { operation, err in
            completion(false)
        }
    }
    
    private func getMySubscription(completion: @escaping (MySubscriptionModel?) -> Void,
                                   error: @escaping (Error, String?) -> Void) {
        let url = "/api/subscriptions/mine"
        manager.get(url, parameters: nil) { operation, responseObject in
            guard let responseDictionary = responseObject as? [String: AnyObject],
                  let subscription = responseDictionary["subscription"] as? [String: Any] else {
                completion(nil)
                return
            }
            
            let decoder = JSONDecoder()
            if let data = try? JSONSerialization.data(withJSONObject: subscription,
                                                      options: .fragmentsAllowed),
                let model = try? decoder.decode(MySubscriptionModel.self, from: data) {
                completion(model)
            } else {
                completion(nil)
            }
            
        } failure: { operation, err in
            error(err, operation?.errorMessage())
        }
    }
    
    public func getMySubscription() -> SignalProducer<(MySubscriptionModel?, String?), NoError> {
        SignalProducer { [weak self] observer, _ in
            guard let self = self else {
                observer.sendCompleted()
                return
            }
            self.getMySubscription { model in
                guard let model = model else {
                    observer.sendCompleted()
                    return
                }
                observer.send(value: (model, nil))
                observer.sendCompleted()
            } error: { err, errorMessage in
                observer.send(value: (nil, errorMessage ?? err.localizedDescription))
            }
        }
    }
    
    public func getAuthConfig(completion: @escaping (AuthConfig?, HFAlert?) -> Void) {
        let url = "/api/config/credentials"
        manager.get(url, parameters: nil) { operation, responseObject in
            guard let responseObject = responseObject as? [String: Any] else {
                completion(nil, nil)
                return
            }

            let authConfig = AuthConfig(fromJson: responseObject)
            completion(authConfig, nil)
            
        } failure: { operation, _ in
            completion(nil, operation?.alert())
        }
    }
    
    public func saveCredentials(params: [String:Any],
                                completion: @escaping (String?, HFAlert?) -> Void) {
        let url = "/api/save_credentials"
        manager.post(url, parameters: params) { operation, responseObject in
            guard let responseObject = responseObject as? [String: Any],
                  let credentialID = responseObject["credential_id"] as? String else {
                completion(nil, nil)
                return
            }

            completion(credentialID, nil)
            
        } failure: { operation, _ in
            completion(nil, operation?.alert())
        }
    }

    public func getExperimentConfigs(
        keys: [String],
        orderToken: String? = nil,
        completion: @escaping ([String: Bool]?) -> Void
    ) {
        let url = "/api/config/experiments"
        let params = keys.count > 0 ? ["names": keys.joined(separator: ",")] : nil

        manager(withOrderToken: orderToken).get(url, parameters: params) { operation, responseObject in
            completion(responseObject as? [String: Bool])
        } failure: { operation, _ in
            completion(nil)
        }
    }
}
